
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .critical { background-color: #dc3545; color: white; }
        .high { background-color: #fd7e14; color: white; }
        .moderate { background-color: #ffc107; color: black; }
        .low { background-color: #28a745; color: white; }
        .section { margin-bottom: 30px; }
        .vulnerability { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .vulnerability.critical { background: #f8d7da; border-color: #f5c6cb; }
        .vulnerability.high { background: #ffeaa7; border-color: #ffd93d; }
        .recommendation { background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Security Audit Report</h1>
            <p class="timestamp">Generated: 2025-07-28T21:09:22.017Z</p>
        </div>
        
        <div class="summary">
            <div class="summary-card critical">
                <h3>Critical</h3>
                <p>0</p>
            </div>
            <div class="summary-card high">
                <h3>High</h3>
                <p>0</p>
            </div>
            <div class="summary-card moderate">
                <h3>Moderate</h3>
                <p>0</p>
            </div>
            <div class="summary-card low">
                <h3>Low</h3>
                <p>0</p>
            </div>
            <div class="summary-card">
                <h3>Outdated</h3>
                <p>11</p>
            </div>
        </div>
        
        <div class="section">
            <h2>🚨 Vulnerabilities</h2>
            <p>✅ No vulnerabilities found!</p>
        </div>
        
        <div class="section">
            <h2>💡 Recommendations</h2>
            <div class="recommendation">Update outdated packages to latest versions</div><div class="recommendation">Specify Node.js and npm versions in engines field</div>
        </div>
    </div>
</body>
</html>