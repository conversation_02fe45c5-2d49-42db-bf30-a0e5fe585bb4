name: Security Audit

on:
  # Run on every push to main branch
  push:
    branches: [ main, master ]
  
  # Run on every pull request
  pull_request:
    branches: [ main, master ]
  
  # Run weekly on Mondays at 9 AM UTC
  schedule:
    - cron: '0 9 * * 1'
  
  # Allow manual trigger
  workflow_dispatch:

jobs:
  security-audit:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
    
    - name: Run custom security audit
      run: npm run security:audit
      continue-on-error: true
    
    - name: Check for outdated packages
      run: npm outdated
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports-node-${{ matrix.node-version }}
        path: security-reports/
        retention-days: 30
    
    - name: Comment PR with security summary
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = './security-reports/security-audit-report.json';
          
          if (fs.existsSync(path)) {
            const report = JSON.parse(fs.readFileSync(path, 'utf8'));
            
            const comment = `## 🔒 Security Audit Summary
            
            | Severity | Count |
            |----------|-------|
            | Critical | ${report.summary.criticalVulnerabilities} |
            | High | ${report.summary.highVulnerabilities} |
            | Moderate | ${report.summary.moderateVulnerabilities} |
            | Low | ${report.summary.lowVulnerabilities} |
            | Outdated Packages | ${report.summary.outdatedPackages} |
            
            ${report.summary.totalVulnerabilities === 0 ? '✅ No vulnerabilities found!' : '⚠️ Vulnerabilities detected. Please review the security report.'}
            
            ### Recommendations:
            ${report.recommendations.map(rec => `- ${rec}`).join('\n')}
            
            <details>
            <summary>View detailed report</summary>
            
            \`\`\`json
            ${JSON.stringify(report, null, 2)}
            \`\`\`
            </details>`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }

  dependency-review:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Dependency Review
      uses: actions/dependency-review-action@v4
      with:
        fail-on-severity: moderate
        allow-licenses: MIT, Apache-2.0, BSD-2-Clause, BSD-3-Clause, ISC
        deny-licenses: GPL-2.0, GPL-3.0

  codeql-analysis:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    permissions:
      actions: read
      contents: read
      security-events: write
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: javascript
        queries: security-and-quality
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v3
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3

  security-scorecard:
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    permissions:
      security-events: write
      id-token: write
      actions: read
      contents: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        persist-credentials: false
    
    - name: Run analysis
      uses: ossf/scorecard-action@v2.3.1
      with:
        results_file: results.sarif
        results_format: sarif
        publish_results: true
    
    - name: Upload SARIF results
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: results.sarif
