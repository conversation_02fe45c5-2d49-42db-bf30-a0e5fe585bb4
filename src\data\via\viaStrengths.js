// VIA Character Strengths detailed data structure
export const viaStrengthsData = {
  'creativity': {
    name: 'Creativity',
    category: 'wisdom',
    description: '<PERSON><PERSON><PERSON><PERSON> secara inovatif dan produktif untuk menemukan cara-cara baru dalam memahami dan melakukan sesuatu',
    highTraits: [
      'Memiliki kemampuan untuk menghasilkan ide-ide baru dan inovatif',
      'Ma<PERSON>u melihat masalah dari perspektif yang berbeda dan unik',
      'Senang bereksperimen dengan pendekatan baru dalam menyelesaikan tugas',
      'Memiliki imajinasi yang kuat dan kemampuan berpikir di luar kotak',
      'Dapat mengombinasikan ide-ide yang berbeda menjadi solusi kreatif'
    ],
    lowTraits: [
      'Lebih menyukai pendekatan yang sudah terbukti dan konvensional',
      '<PERSON><PERSON>a lebih nyaman mengikuti prosedur yang telah ditetapkan',
      '<PERSON>rang tertarik untuk mencoba metode atau ide yang belum teruji',
      '<PERSON><PERSON>h fokus pada efisiensi daripada inovasi',
      'Mengutamakan stabilitas dan prediktabilitas dalam pendekatan'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan inovasi dan pemikiran kreatif, seperti desain, penelitian dan pengembangan, atau bidang seni dan media.',
      low: 'Lebih sesuai untuk peran yang membutuhkan konsistensi dan keandalan, seperti operasional, administrasi, atau implementasi sistem yang sudah ada.'
    }
  },
  'curiosity': {
    name: 'Curiosity',
    category: 'wisdom',
    description: 'Memiliki ketertarikan yang tulus untuk terus mengeksplorasi pengalaman dan pengetahuan baru',
    highTraits: [
      'Memiliki rasa ingin tahu yang tinggi terhadap berbagai topik dan pengalaman',
      'Senang mengeksplorasi ide-ide baru dan mempelajari hal-hal yang belum diketahui',
      'Aktif mencari informasi dan pengetahuan dari berbagai sumber',
      'Terbuka terhadap pengalaman baru dan tantangan intelektual',
      'Memiliki motivasi intrinsik untuk terus belajar dan berkembang'
    ],
    lowTraits: [
      'Lebih fokus pada area keahlian yang sudah dikuasai',
      'Merasa cukup dengan pengetahuan yang sudah dimiliki',
      'Kurang tertarik untuk mengeksplorasi topik di luar bidang utama',
      'Lebih menyukai rutinitas dan hal-hal yang sudah familiar',
      'Mengutamakan pendalaman daripada perluasan pengetahuan'
    ],
    implications: {
      high: 'Ideal untuk peran yang melibatkan penelitian, pembelajaran berkelanjutan, atau eksplorasi bidang baru seperti R&D, jurnalisme, atau konsultasi.',
      low: 'Lebih cocok untuk peran yang membutuhkan keahlian mendalam di bidang spesifik dan konsistensi dalam penerapan pengetahuan yang sudah ada.'
    }
  },
  'judgment': {
    name: 'Critical Thinking',
    category: 'wisdom',
    description: 'Menganalisis segala sesuatu secara menyeluruh dan mempertimbangkan berbagai sudut pandang sebelum mengambil keputusan',
    highTraits: [
      'Mampu menganalisis informasi secara objektif dan menyeluruh',
      'Memiliki kemampuan untuk mengevaluasi argumen dan bukti dengan kritis',
      'Dapat mengidentifikasi bias dan kelemahan dalam pemikiran',
      'Senang mempertimbangkan berbagai perspektif sebelum mengambil keputusan',
      'Memiliki standar tinggi untuk kualitas pemikiran dan reasoning'
    ],
    lowTraits: [
      'Lebih mengandalkan intuisi dan perasaan dalam pengambilan keputusan',
      'Cenderung menerima informasi tanpa analisis mendalam',
      'Kurang tertarik pada detail dan nuansa dalam argumen',
      'Lebih menyukai keputusan yang cepat daripada analisis yang panjang',
      'Mengutamakan harmoni daripada kritik konstruktif'
    ],
    implications: {
      high: 'Sangat sesuai untuk peran yang membutuhkan analisis mendalam dan pengambilan keputusan strategis, seperti manajemen senior, audit, atau konsultasi strategis.',
      low: 'Lebih cocok untuk peran yang membutuhkan empati dan hubungan interpersonal yang kuat, atau pekerjaan yang bersifat eksekutif dengan panduan yang jelas.'
    }
  },
  'love_of_learning': {
    name: 'Love of Learning',
    category: 'wisdom',
    description: 'Menguasai keterampilan, topik, dan pengetahuan baru secara berkelanjutan',
    highTraits: [
      'Memiliki motivasi tinggi untuk terus mengembangkan pengetahuan dan keterampilan',
      'Senang mengikuti pelatihan, kursus, atau program pengembangan diri',
      'Aktif mencari peluang untuk mempelajari hal-hal baru di bidang pekerjaan',
      'Memiliki kemampuan untuk belajar secara mandiri dan berkelanjutan',
      'Merasa energik dan termotivasi ketika menghadapi tantangan pembelajaran baru'
    ],
    lowTraits: [
      'Lebih fokus pada penerapan pengetahuan yang sudah dimiliki',
      'Merasa cukup dengan tingkat keahlian saat ini',
      'Kurang tertarik pada program pengembangan atau pelatihan tambahan',
      'Lebih menyukai stabilitas dalam rutinitas kerja',
      'Mengutamakan efisiensi dalam tugas yang sudah dikuasai'
    ],
    implications: {
      high: 'Ideal untuk lingkungan kerja yang dinamis dan berkembang pesat, seperti teknologi, akademik, atau bidang yang membutuhkan adaptasi berkelanjutan.',
      low: 'Lebih sesuai untuk peran yang membutuhkan keahlian stabil dan konsisten, seperti operasional rutin atau spesialisasi teknis yang sudah mapan.'
    }
  },
  'loveOfLearning': {
    name: 'Love of Learning',
    category: 'wisdom',
    description: 'Menguasai keterampilan, topik, dan pengetahuan baru secara berkelanjutan',
    highTraits: [
      'Memiliki motivasi tinggi untuk terus mengembangkan pengetahuan dan keterampilan',
      'Senang mengikuti pelatihan, kursus, atau program pengembangan diri',
      'Aktif mencari peluang untuk mempelajari hal-hal baru di bidang pekerjaan',
      'Memiliki kemampuan untuk belajar secara mandiri dan berkelanjutan',
      'Merasa energik dan termotivasi ketika menghadapi tantangan pembelajaran baru'
    ],
    lowTraits: [
      'Lebih fokus pada penerapan pengetahuan yang sudah dimiliki',
      'Merasa cukup dengan tingkat keahlian saat ini',
      'Kurang tertarik pada program pengembangan atau pelatihan tambahan',
      'Lebih menyukai stabilitas dalam rutinitas kerja',
      'Mengutamakan efisiensi dalam tugas yang sudah dikuasai'
    ],
    implications: {
      high: 'Ideal untuk lingkungan kerja yang dinamis dan berkembang pesat, seperti teknologi, akademik, atau bidang yang membutuhkan adaptasi berkelanjutan.',
      low: 'Lebih sesuai untuk peran yang membutuhkan keahlian stabil dan konsisten, seperti operasional rutin atau spesialisasi teknis yang sudah mapan.'
    }
  },
  'perspective': {
    name: 'Perspective',
    category: 'wisdom',
    description: 'Mampu memberikan nasihat yang bijaksana dan memiliki cara pandang yang luas terhadap kehidupan',
    highTraits: [
      'Mampu memberikan nasihat yang bijaksana dan perspektif yang luas',
      'Memiliki kemampuan untuk melihat gambaran besar dan konteks yang lebih luas',
      'Dapat membantu orang lain memahami situasi dari berbagai sudut pandang',
      'Memiliki pengalaman hidup yang kaya dan dapat mengambil pelajaran darinya',
      'Dipercaya sebagai sumber kebijaksaan dan panduan oleh orang lain'
    ],
    lowTraits: [
      'Lebih fokus pada detail dan aspek teknis daripada gambaran besar',
      'Kurang nyaman memberikan nasihat atau panduan kepada orang lain',
      'Lebih menyukai peran eksekutif daripada advisory',
      'Mengutamakan tindakan praktis daripada refleksi filosofis',
      'Lebih tertarik pada solusi konkret daripada pemahaman konseptual'
    ],
    implications: {
      high: 'Sangat cocok untuk peran kepemimpinan, mentoring, atau konsultasi yang membutuhkan kebijaksaan dan pandangan strategis jangka panjang.',
      low: 'Lebih sesuai untuk peran teknis atau operasional yang membutuhkan fokus pada detail dan implementasi praktis.'
    }
  },
  'bravery': {
    name: 'Bravery',
    category: 'courage',
    description: 'Tidak menghindar dari ancaman, tantangan, kesulitan, atau rasa sakit',
    highTraits: [
      'Berani menghadapi tantangan dan situasi yang sulit atau berisiko',
      'Mampu mengambil keputusan sulit meskipun ada tekanan atau ketidakpastian',
      'Tidak mudah mundur ketika menghadapi hambatan atau kritik',
      'Bersedia membela prinsip dan nilai-nilai meskipun tidak populer',
      'Memiliki ketahanan mental yang kuat dalam menghadapi adversitas'
    ],
    lowTraits: [
      'Lebih menyukai lingkungan kerja yang stabil dan dapat diprediksi',
      'Cenderung menghindari konflik atau situasi yang kontroversial',
      'Lebih nyaman dengan tugas-tugas yang sudah familiar dan aman',
      'Mengutamakan konsensus dan harmoni daripada mengambil posisi yang berani',
      'Lebih suka bekerja dalam tim daripada mengambil tanggung jawab individual yang besar'
    ],
    implications: {
      high: 'Ideal untuk peran kepemimpinan dalam situasi krisis, change management, atau bidang yang membutuhkan pengambilan risiko strategis.',
      low: 'Lebih cocok untuk peran yang membutuhkan stabilitas, kerja sama tim, dan lingkungan kerja yang harmonis dan mendukung.'
    }
  },
  'perseverance': {
    name: 'Perseverance',
    category: 'courage',
    description: 'Menyelesaikan apa yang telah dimulai dan tetap konsisten meskipun menghadapi hambatan',
    highTraits: [
      'Memiliki kemampuan untuk menyelesaikan tugas hingga tuntas meskipun menghadapi hambatan',
      'Tidak mudah menyerah ketika menghadapi kesulitan atau kegagalan',
      'Mampu mempertahankan motivasi dan fokus dalam jangka panjang',
      'Memiliki disiplin diri yang kuat untuk terus bekerja menuju tujuan',
      'Dapat bangkit kembali setelah mengalami kemunduran atau kegagalan'
    ],
    lowTraits: [
      'Cenderung beralih ke tugas lain ketika menghadapi hambatan',
      'Lebih menyukai proyek dengan hasil yang cepat terlihat',
      'Kurang sabar dengan proses yang membutuhkan waktu lama',
      'Mudah kehilangan motivasi ketika tidak melihat progress yang jelas',
      'Lebih tertarik pada variasi daripada konsistensi jangka panjang'
    ],
    implications: {
      high: 'Sangat cocok untuk proyek jangka panjang, penelitian, atau bidang yang membutuhkan konsistensi dan ketekunan tinggi.',
      low: 'Lebih sesuai untuk peran yang dinamis dengan variasi tugas dan hasil yang cepat terlihat.'
    }
  },
  'honesty': {
    name: 'Honesty',
    category: 'courage',
    description: 'Selalu berkata jujur dan menampilkan diri secara autentik',
    highTraits: [
      'Selalu berusaha untuk jujur dan transparan dalam komunikasi',
      'Memiliki integritas yang tinggi dan konsisten antara kata dan tindakan',
      'Berani menyampaikan kebenaran meskipun tidak populer',
      'Dapat dipercaya untuk memberikan informasi yang akurat',
      'Menunjukkan keaslian diri tanpa berpura-pura atau menyembunyikan kepribadian'
    ],
    lowTraits: [
      'Kadang menyesuaikan komunikasi untuk menghindari konflik',
      'Lebih mempertimbangkan dampak sosial daripada kebenaran mutlak',
      'Cenderung diplomatik dalam menyampaikan informasi sensitif',
      'Mengutamakan harmoni hubungan daripada transparansi total',
      'Lebih fleksibel dalam presentasi diri sesuai situasi'
    ],
    implications: {
      high: 'Ideal untuk peran yang membutuhkan kepercayaan tinggi seperti audit, konsultasi, atau posisi kepemimpinan yang membutuhkan integritas.',
      low: 'Lebih cocok untuk peran yang membutuhkan diplomasi dan kemampuan adaptasi sosial yang tinggi.'
    }
  },
  'zest': {
    name: 'Zest',
    category: 'courage',
    description: 'Menjalani hidup dengan semangat dan energi yang tinggi',
    highTraits: [
      'Memiliki energi dan antusiasme yang tinggi dalam menghadapi tugas',
      'Mampu memotivasi dan menginspirasi orang lain dengan semangat',
      'Menunjukkan passion yang kuat terhadap pekerjaan dan aktivitas',
      'Memiliki vitalitas yang dapat dipertahankan dalam jangka panjang',
      'Melihat peluang dan tantangan dengan optimisme dan kegembiraan'
    ],
    lowTraits: [
      'Lebih tenang dan stabil dalam pendekatan terhadap tugas',
      'Mengutamakan efisiensi daripada antusiasme dalam bekerja',
      'Lebih suka bekerja dengan ritme yang konsisten dan terkendali',
      'Kurang menunjukkan emosi atau excitement secara eksternal',
      'Lebih fokus pada hasil daripada proses yang energik'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan motivasi tim, sales, atau bidang kreatif yang membutuhkan energi tinggi.',
      low: 'Lebih sesuai untuk peran analitis, teknis, atau yang membutuhkan konsistensi dan stabilitas.'
    }
  },
  'love': {
    name: 'Love',
    category: 'humanity',
    description: 'Kemampuan membangun dan menghargai hubungan yang erat dengan orang lain',
    highTraits: [
      'Mampu membentuk dan mempertahankan hubungan yang mendalam dan bermakna',
      'Menunjukkan kasih sayang dan perhatian yang tulus kepada orang lain',
      'Memiliki kemampuan empati yang tinggi dan dapat memahami perasaan orang lain',
      'Bersedia menginvestasikan waktu dan energi untuk hubungan personal',
      'Menciptakan lingkungan yang hangat dan mendukung bagi orang-orang terdekat'
    ],
    lowTraits: [
      'Lebih fokus pada tugas dan pencapaian daripada hubungan personal',
      'Cenderung menjaga jarak emosional dalam interaksi profesional',
      'Lebih nyaman dengan hubungan yang formal dan terstruktur',
      'Mengutamakan efisiensi daripada koneksi emosional',
      'Lebih suka bekerja secara independen daripada dalam tim yang erat'
    ],
    implications: {
      high: 'Ideal untuk peran yang melibatkan mentoring, counseling, HR, atau kepemimpinan yang membutuhkan koneksi personal yang kuat.',
      low: 'Lebih cocok untuk peran teknis, analitis, atau yang membutuhkan objektivitas dan fokus pada hasil.'
    }
  },
  'kindness': {
    name: 'Kindness',
    category: 'humanity',
    description: 'Suka membantu, berbuat baik, dan peduli terhadap orang lain',
    highTraits: [
      'Selalu siap membantu orang lain tanpa mengharapkan imbalan',
      'Menunjukkan kepedulian dan perhatian terhadap kesejahteraan rekan kerja',
      'Memiliki kemampuan untuk melihat kebutuhan orang lain dan bertindak untuk membantu',
      'Menciptakan lingkungan kerja yang supportive dan inclusive',
      'Bersedia mengorbankan waktu dan sumber daya untuk membantu orang lain'
    ],
    lowTraits: [
      'Lebih fokus pada tanggung jawab dan tugas pribadi',
      'Mengutamakan efisiensi dan produktivitas daripada membantu orang lain',
      'Cenderung menunggu diminta sebelum menawarkan bantuan',
      'Lebih objektif dalam menilai situasi tanpa terlalu terlibat emosional',
      'Mengharapkan orang lain untuk mandiri dan menyelesaikan masalah sendiri'
    ],
    implications: {
      high: 'Sangat cocok untuk peran dalam customer service, healthcare, education, atau posisi yang membutuhkan pelayanan dan dukungan kepada orang lain.',
      low: 'Lebih sesuai untuk peran yang membutuhkan objektivitas, analisis, atau fokus pada pencapaian target individual.'
    }
  },
  'social_intelligence': {
    name: 'Social Intelligence',
    category: 'humanity',
    description: 'Memahami motivasi dan perasaan diri sendiri maupun orang lain',
    highTraits: [
      'Mampu membaca dan memahami dinamika sosial dengan akurat',
      'Memiliki kemampuan untuk menyesuaikan komunikasi sesuai dengan audiens',
      'Dapat mengidentifikasi motivasi dan emosi orang lain dengan tepat',
      'Mahir dalam navigasi politik organisasi dan hubungan interpersonal',
      'Memiliki awareness yang tinggi terhadap dampak perilaku sendiri pada orang lain'
    ],
    lowTraits: [
      'Lebih fokus pada konten daripada konteks sosial dalam komunikasi',
      'Cenderung straightforward dan direct dalam interaksi',
      'Kurang memperhatikan nuansa emosional dalam situasi sosial',
      'Lebih nyaman dengan komunikasi yang eksplisit dan jelas',
      'Mengutamakan substansi daripada diplomasi dalam berinteraksi'
    ],
    implications: {
      high: 'Ideal untuk peran dalam sales, negotiation, public relations, atau kepemimpinan yang membutuhkan kemampuan interpersonal yang tinggi.',
      low: 'Lebih cocok untuk peran teknis, penelitian, atau yang membutuhkan fokus pada detail dan akurasi daripada dinamika sosial.'
    }
  },
  'socialIntelligence': {
    name: 'Social Intelligence',
    category: 'humanity',
    description: 'Memahami motivasi dan perasaan diri sendiri maupun orang lain',
    highTraits: [
      'Mampu membaca dan memahami dinamika sosial dengan akurat',
      'Memiliki kemampuan untuk menyesuaikan komunikasi sesuai dengan audiens',
      'Dapat mengidentifikasi motivasi dan emosi orang lain dengan tepat',
      'Mahir dalam navigasi politik organisasi dan hubungan interpersonal',
      'Memiliki awareness yang tinggi terhadap dampak perilaku sendiri pada orang lain'
    ],
    lowTraits: [
      'Lebih fokus pada konten daripada konteks sosial dalam komunikasi',
      'Cenderung straightforward dan direct dalam interaksi',
      'Kurang memperhatikan nuansa emosional dalam situasi sosial',
      'Lebih nyaman dengan komunikasi yang eksplisit dan jelas',
      'Mengutamakan substansi daripada diplomasi dalam berinteraksi'
    ],
    implications: {
      high: 'Ideal untuk peran dalam sales, negotiation, public relations, atau kepemimpinan yang membutuhkan kemampuan interpersonal yang tinggi.',
      low: 'Lebih cocok untuk peran teknis, penelitian, atau yang membutuhkan fokus pada detail dan akurasi daripada dinamika sosial.'
    }
  },
  'teamwork': {
    name: 'Teamwork',
    category: 'justice',
    description: 'Kepedulian terhadap kelompok, tanggung jawab sosial, loyalitas, dan kerja sama tim',
    highTraits: [
      'Memiliki komitmen yang kuat terhadap kesuksesan tim dan organisasi',
      'Bersedia mengorbankan kepentingan pribadi untuk kepentingan bersama',
      'Menunjukkan loyalitas dan dedikasi yang tinggi terhadap kelompok',
      'Aktif berkontribusi dalam menciptakan lingkungan kerja yang kolaboratif',
      'Memiliki sense of responsibility yang kuat terhadap komunitas dan organisasi'
    ],
    lowTraits: [
      'Lebih fokus pada pencapaian individual daripada tim',
      'Cenderung bekerja secara independen dan mandiri',
      'Mengutamakan efisiensi personal daripada proses kolaboratif',
      'Kurang tertarik pada aktivitas team building atau sosial',
      'Lebih objektif dalam menilai kontribusi tanpa bias kelompok'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan kolaborasi tinggi, project management, atau posisi yang membutuhkan team cohesion yang kuat.',
      low: 'Lebih sesuai untuk peran individual contributor, research, atau yang membutuhkan fokus dan konsentrasi tinggi.'
    }
  },
  'fairness': {
    name: 'Fairness',
    category: 'justice',
    description: 'Memperlakukan semua orang secara adil dan setara sesuai prinsip keadilan',
    highTraits: [
      'Selalu berusaha untuk berlaku adil dan objektif dalam setiap situasi',
      'Memiliki prinsip yang kuat tentang kesetaraan dan keadilan',
      'Mampu membuat keputusan yang tidak bias dan berdasarkan merit',
      'Bersedia membela orang yang diperlakukan tidak adil',
      'Konsisten dalam menerapkan standar dan aturan untuk semua orang'
    ],
    lowTraits: [
      'Lebih fleksibel dalam menerapkan aturan sesuai konteks',
      'Cenderung mempertimbangkan faktor personal dalam pengambilan keputusan',
      'Lebih pragmatis daripada idealis dalam pendekatan terhadap keadilan',
      'Mengutamakan hasil daripada proses yang sempurna',
      'Lebih toleran terhadap perbedaan perlakuan jika ada alasan yang valid'
    ],
    implications: {
      high: 'Ideal untuk peran dalam HR, legal, audit, atau posisi kepemimpinan yang membutuhkan pengambilan keputusan yang adil dan objektif.',
      low: 'Lebih cocok untuk peran yang membutuhkan fleksibilitas, adaptasi, atau pendekatan yang lebih personal dan kontekstual.'
    }
  },
  'leadership': {
    name: 'Leadership',
    category: 'justice',
    description: 'Mendorong kelompok untuk mencapai tujuan bersama secara efektif',
    highTraits: [
      'Memiliki kemampuan natural untuk mempengaruhi dan menginspirasi orang lain',
      'Berani mengambil tanggung jawab dan membuat keputusan sulit',
      'Mampu mengorganisir dan mengarahkan tim menuju tujuan bersama',
      'Memiliki visi yang jelas dan dapat mengkomunikasikannya dengan efektif',
      'Bersedia mengambil inisiatif dan memimpin perubahan'
    ],
    lowTraits: [
      'Lebih nyaman sebagai individual contributor atau follower',
      'Cenderung menghindari tanggung jawab pengambilan keputusan untuk orang lain',
      'Lebih suka bekerja dengan panduan yang jelas daripada membuat arah sendiri',
      'Kurang tertarik pada aspek politik dan dinamika kekuasaan',
      'Mengutamakan keahlian teknis daripada kemampuan manajerial'
    ],
    implications: {
      high: 'Sangat cocok untuk posisi managerial, executive, atau peran yang membutuhkan kemampuan untuk memimpin dan mengarahkan orang lain.',
      low: 'Lebih sesuai untuk peran specialist, technical expert, atau posisi yang membutuhkan keahlian mendalam tanpa tanggung jawab manajerial.'
    }
  },
  'forgiveness': {
    name: 'Forgiveness',
    category: 'temperance',
    description: 'Memaafkan kesalahan orang lain, memberi kesempatan kedua, dan menunjukkan belas kasih',
    highTraits: [
      'Mampu melepaskan dendam dan memberikan kesempatan kedua kepada orang lain',
      'Memiliki kemampuan untuk memisahkan tindakan dari pribadi seseorang',
      'Bersedia melupakan kesalahan masa lalu dan fokus pada masa depan',
      'Menunjukkan compassion dan understanding terhadap kelemahan manusia',
      'Dapat mempertahankan hubungan meskipun pernah mengalami konflik'
    ],
    lowTraits: [
      'Lebih sulit untuk melupakan kesalahan atau pelanggaran',
      'Cenderung menjaga jarak dengan orang yang pernah mengecewakan',
      'Mengutamakan akuntabilitas dan konsekuensi atas tindakan',
      'Lebih protektif terhadap diri sendiri dan orang-orang terdekat',
      'Memiliki standar tinggi untuk kepercayaan dan integritas'
    ],
    implications: {
      high: 'Ideal untuk peran dalam conflict resolution, counseling, HR, atau posisi yang membutuhkan kemampuan untuk mengelola hubungan yang kompleks.',
      low: 'Lebih cocok untuk peran yang membutuhkan standar tinggi, quality control, atau posisi yang membutuhkan konsistensi dan akuntabilitas.'
    }
  },
  'humility': {
    name: 'Humility',
    category: 'temperance',
    description: 'Rendah hati dan membiarkan pencapaian berbicara dengan sendirinya',
    highTraits: [
      'Tidak sombong atau membanggakan pencapaian secara berlebihan',
      'Bersedia mengakui kesalahan dan belajar dari orang lain',
      'Memiliki perspektif yang realistis tentang kemampuan dan keterbatasan diri',
      'Menghargai kontribusi orang lain dan memberikan credit yang pantas',
      'Lebih fokus pada pembelajaran dan improvement daripada pengakuan'
    ],
    lowTraits: [
      'Lebih percaya diri dalam mempromosikan kemampuan dan pencapaian',
      'Cenderung mengambil credit untuk kesuksesan dan pencapaian',
      'Memiliki confidence yang tinggi dalam kemampuan dan judgment',
      'Kurang ragu untuk memimpin atau mengambil posisi dominan',
      'Lebih assertive dalam menyampaikan pendapat dan ide'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan pembelajaran berkelanjutan, research, atau posisi yang membutuhkan kolaborasi dan team harmony.',
      low: 'Lebih sesuai untuk peran leadership, sales, atau posisi yang membutuhkan confidence dan kemampuan untuk mempromosikan ide atau produk.'
    }
  },
  'prudence': {
    name: 'Prudence',
    category: 'temperance',
    description: 'Berhati-hati dalam mengambil keputusan, menghindari risiko yang tidak perlu, dan menjaga tindakan agar tidak disesali di kemudian hari',
    highTraits: [
      'Selalu mempertimbangkan konsekuensi sebelum mengambil keputusan',
      'Memiliki kemampuan untuk menahan diri dari tindakan impulsif',
      'Berhati-hati dalam komunikasi dan menghindari pernyataan yang merugikan',
      'Mengutamakan perencanaan dan persiapan yang matang',
      'Mampu mengelola risiko dengan bijaksana dan calculated'
    ],
    lowTraits: [
      'Lebih spontan dan fleksibel dalam pengambilan keputusan',
      'Cenderung mengambil risiko untuk mendapatkan hasil yang lebih besar',
      'Lebih direct dan straightforward dalam komunikasi',
      'Kurang sabar dengan proses perencanaan yang terlalu detail',
      'Lebih tertarik pada action daripada analysis yang berkepanjangan'
    ],
    implications: {
      high: 'Ideal untuk peran dalam risk management, financial planning, legal, atau posisi yang membutuhkan careful decision making.',
      low: 'Lebih cocok untuk peran yang membutuhkan quick decision making, innovation, atau lingkungan yang dinamis dan fast-paced.'
    }
  },
  'self_regulation': {
    name: 'Self-Regulation',
    category: 'temperance',
    description: 'Mengatur perasaan dan perilaku secara disiplin serta mampu mengendalikan dorongan dan emosi',
    highTraits: [
      'Memiliki kontrol diri yang kuat terhadap emosi dan impuls',
      'Mampu mempertahankan fokus dan disiplin dalam jangka panjang',
      'Bersedia menunda gratifikasi untuk mencapai tujuan yang lebih besar',
      'Dapat mengelola stress dan tekanan dengan tenang dan terkendali',
      'Konsisten dalam menjalankan rutinitas dan komitmen yang telah dibuat'
    ],
    lowTraits: [
      'Lebih ekspresif dan spontan dalam menunjukkan emosi',
      'Cenderung mengikuti mood dan energy level yang berfluktuasi',
      'Lebih fleksibel dalam mengubah rencana sesuai situasi',
      'Kurang sabar dengan rutinitas yang monoton atau rigid',
      'Lebih responsif terhadap stimulus eksternal dan perubahan lingkungan'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan consistency, long-term projects, atau posisi yang membutuhkan emotional stability.',
      low: 'Lebih sesuai untuk peran kreatif, dynamic environment, atau posisi yang membutuhkan adaptability dan responsiveness.'
    }
  },
  'selfRegulation': {
    name: 'Self-Regulation',
    category: 'temperance',
    description: 'Mengatur perasaan dan perilaku secara disiplin serta mampu mengendalikan dorongan dan emosi',
    highTraits: [
      'Memiliki kontrol diri yang kuat terhadap emosi dan impuls',
      'Mampu mempertahankan fokus dan disiplin dalam jangka panjang',
      'Bersedia menunda gratifikasi untuk mencapai tujuan yang lebih besar',
      'Dapat mengelola stress dan tekanan dengan tenang dan terkendali',
      'Konsisten dalam menjalankan rutinitas dan komitmen yang telah dibuat'
    ],
    lowTraits: [
      'Lebih ekspresif dan spontan dalam menunjukkan emosi',
      'Cenderung mengikuti mood dan energy level yang berfluktuasi',
      'Lebih fleksibel dalam mengubah rencana sesuai situasi',
      'Kurang sabar dengan rutinitas yang monoton atau rigid',
      'Lebih responsif terhadap stimulus eksternal dan perubahan lingkungan'
    ],
    implications: {
      high: 'Sangat cocok untuk peran yang membutuhkan consistency, long-term projects, atau posisi yang membutuhkan emotional stability.',
      low: 'Lebih sesuai untuk peran kreatif, dynamic environment, atau posisi yang membutuhkan adaptability dan responsiveness.'
    }
  },
  'appreciation_of_beauty': {
    name: 'Appreciation of Beauty',
    category: 'transcendence',
    description: 'Menyadari dan menghargai keindahan, keunggulan, serta performa terbaik di berbagai bidang',
    highTraits: [
      'Memiliki kepekaan tinggi terhadap keindahan dalam berbagai bentuk',
      'Dapat menghargai excellence dan craftsmanship dalam pekerjaan',
      'Termotivasi oleh aesthetic dan kualitas dalam lingkungan kerja',
      'Memiliki kemampuan untuk melihat dan menciptakan harmony dalam design',
      'Menghargai detail dan finesse dalam hasil kerja'
    ],
    lowTraits: [
      'Lebih fokus pada fungsi daripada form dalam pendekatan kerja',
      'Mengutamakan efisiensi dan praktikalitas daripada aesthetic',
      'Kurang memperhatikan aspek visual atau artistic dalam pekerjaan',
      'Lebih tertarik pada substance daripada presentation',
      'Cenderung pragmatis dalam menilai kualitas berdasarkan utility'
    ],
    implications: {
      high: 'Ideal untuk peran dalam design, arts, architecture, atau bidang yang membutuhkan aesthetic sense dan appreciation untuk quality.',
      low: 'Lebih cocok untuk peran teknis, engineering, atau yang membutuhkan fokus pada functionality dan efficiency.'
    }
  },
  'appreciationOfBeauty': {
    name: 'Appreciation of Beauty',
    category: 'transcendence',
    description: 'Menyadari dan menghargai keindahan, keunggulan, serta performa terbaik di berbagai bidang',
    highTraits: [
      'Memiliki kepekaan tinggi terhadap keindahan dalam berbagai bentuk',
      'Dapat menghargai excellence dan craftsmanship dalam pekerjaan',
      'Termotivasi oleh aesthetic dan kualitas dalam lingkungan kerja',
      'Memiliki kemampuan untuk melihat dan menciptakan harmony dalam design',
      'Menghargai detail dan finesse dalam hasil kerja'
    ],
    lowTraits: [
      'Lebih fokus pada fungsi daripada form dalam pendekatan kerja',
      'Mengutamakan efisiensi dan praktikalitas daripada aesthetic',
      'Kurang memperhatikan aspek visual atau artistic dalam pekerjaan',
      'Lebih tertarik pada substance daripada presentation',
      'Cenderung pragmatis dalam menilai kualitas berdasarkan utility'
    ],
    implications: {
      high: 'Ideal untuk peran dalam design, arts, architecture, atau bidang yang membutuhkan aesthetic sense dan appreciation untuk quality.',
      low: 'Lebih cocok untuk peran teknis, engineering, atau yang membutuhkan fokus pada functionality dan efficiency.'
    }
  },
  'gratitude': {
    name: 'Gratitude',
    category: 'transcendence',
    description: 'Selalu menyadari dan bersyukur atas hal-hal baik yang terjadi',
    highTraits: [
      'Selalu menghargai dan berterima kasih atas dukungan yang diterima',
      'Memiliki perspektif positif dan dapat melihat sisi baik dalam situasi',
      'Aktif mengekspresikan appreciation kepada rekan kerja dan atasan',
      'Mampu mempertahankan motivasi dengan menghargai pencapaian kecil',
      'Menciptakan lingkungan kerja yang positif dengan sikap appreciative'
    ],
    lowTraits: [
      'Lebih fokus pada area yang perlu diperbaiki daripada yang sudah baik',
      'Cenderung melihat dukungan sebagai hal yang wajar atau expected',
      'Kurang ekspresif dalam menunjukkan appreciation',
      'Lebih critical dan analytical dalam mengevaluasi situasi',
      'Mengutamakan improvement daripada celebration atas pencapaian'
    ],
    implications: {
      high: 'Sangat cocok untuk peran dalam team leadership, customer relations, atau posisi yang membutuhkan positive team dynamics.',
      low: 'Lebih sesuai untuk peran dalam quality assurance, analysis, atau posisi yang membutuhkan critical thinking dan improvement focus.'
    }
  },
  'hope': {
    name: 'Hope',
    category: 'transcendence',
    description: 'Memiliki optimisme, orientasi ke masa depan, dan pandangan positif terhadap peluang yang akan datang',
    highTraits: [
      'Memiliki optimisme yang kuat tentang masa depan dan kemungkinan',
      'Mampu mempertahankan motivasi meskipun menghadapi kemunduran',
      'Berfokus pada peluang dan potensi daripada hambatan',
      'Memiliki visi yang jelas tentang tujuan jangka panjang',
      'Dapat menginspirasi orang lain dengan perspektif positif tentang masa depan'
    ],
    lowTraits: [
      'Lebih realistis dan cautious dalam memandang masa depan',
      'Cenderung fokus pada situasi present daripada planning jangka panjang',
      'Lebih skeptical terhadap perubahan atau improvement yang besar',
      'Mengutamakan stability dan predictability daripada growth potential',
      'Lebih praktis dalam menilai kemungkinan dan risiko'
    ],
    implications: {
      high: 'Ideal untuk peran dalam strategic planning, change management, atau posisi yang membutuhkan vision dan future orientation.',
      low: 'Lebih cocok untuk peran dalam operations, maintenance, atau posisi yang membutuhkan focus pada current state dan stability.'
    }
  },
  'humor': {
    name: 'Humor',
    category: 'transcendence',
    description: 'Menyukai humor, mampu membuat orang lain tersenyum, dan melihat sisi ringan dalam berbagai situasi',
    highTraits: [
      'Mampu menggunakan humor untuk mencairkan suasana dan mengurangi tension',
      'Memiliki kemampuan untuk melihat sisi lucu dalam situasi sulit',
      'Dapat menciptakan lingkungan kerja yang enjoyable dan relaxed',
      'Menggunakan humor sebagai tool untuk building rapport dan connection',
      'Memiliki perspective yang light-hearted terhadap challenges'
    ],
    lowTraits: [
      'Lebih serius dan formal dalam pendekatan terhadap pekerjaan',
      'Cenderung menjaga profesionalitas dengan menghindari humor',
      'Lebih fokus pada task completion daripada team enjoyment',
      'Kurang comfortable dengan banter atau casual interaction',
      'Mengutamakan efficiency daripada creating positive atmosphere'
    ],
    implications: {
      high: 'Sangat cocok untuk peran dalam team building, customer service, atau posisi yang membutuhkan interpersonal skills dan team morale.',
      low: 'Lebih sesuai untuk peran formal, technical, atau yang membutuhkan serious focus dan professional demeanor.'
    }
  },
  'spirituality': {
    name: 'Spirituality',
    category: 'transcendence',
    description: 'Memiliki keyakinan yang kuat tentang tujuan dan makna hidup yang lebih tinggi',
    highTraits: [
      'Memiliki sense of purpose yang kuat dan meaning dalam pekerjaan',
      'Mampu melihat kontribusi pekerjaan dalam konteks yang lebih besar',
      'Termotivasi oleh values dan principles yang mendalam',
      'Memiliki perspective yang holistic tentang life dan career',
      'Dapat menemukan fulfillment melalui service dan contribution'
    ],
    lowTraits: [
      'Lebih fokus pada aspek praktis dan tangible dari pekerjaan',
      'Cenderung memisahkan personal values dari professional responsibilities',
      'Mengutamakan achievement dan recognition daripada meaning',
      'Lebih comfortable dengan goals yang specific dan measurable',
      'Fokus pada immediate results daripada long-term impact'
    ],
    implications: {
      high: 'Ideal untuk peran dalam non-profit, education, healthcare, atau posisi yang memiliki clear social impact dan purpose.',
      low: 'Lebih cocok untuk peran dalam business, finance, atau yang membutuhkan focus pada measurable outcomes dan practical results.'
    }
  }
};
